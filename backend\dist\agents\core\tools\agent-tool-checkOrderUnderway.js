"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkOrderUnderway = void 0;
exports.checkOrderUnderwayTool = checkOrderUnderwayTool;
const dist_1 = require("../../../../../ai-agent/dist");
const customer_service_1 = require("../../services/customer.service");
const shared_1 = require("./shared");
const typeorm_1 = require("typeorm");
async function checkOrderUnderwayTool(params, db, conversationUuid) {
    const { customerEmail, customerName, items } = params || {};
    if (!customerEmail && !customerName) {
        throw new Error('Either customerEmail or customerName is required');
    }
    if (!db || !db.isInitialized) {
        throw new Error('Database connection is not properly initialized');
    }
    const conversationRepo = db.getRepository('Conversation');
    const customerRepo = db.getRepository('Customer');
    const orderRepo = db.getRepository('Order');
    const convo = await conversationRepo.findOne({
        where: { uuid: conversationUuid, isDeleted: false },
        select: ['id', 'userId', 'storeId', 'createdBy', 'context'],
    });
    if (!convo)
        throw new Error('Conversation not found');
    let customerIdBigInt;
    if (customerEmail) {
        const foundCustomers = await (0, customer_service_1.filterCustomers)({ email: customerEmail, storeId: convo.storeId }, db);
        if (!foundCustomers || foundCustomers.length === 0) {
            return { underway: false, message: 'No existing customer found for the provided email.' };
        }
        const exact = foundCustomers.find((c) => (c.email || '').toLowerCase() === customerEmail.toLowerCase());
        customerIdBigInt = (exact || foundCustomers[0]).id;
    }
    else {
        const foundCustomers = await (0, customer_service_1.filterCustomers)({ name: customerName, storeId: convo.storeId }, db);
        if (!foundCustomers || foundCustomers.length === 0) {
            return { underway: false, message: 'No existing customer found for the provided name.' };
        }
        customerIdBigInt = foundCustomers[0].id;
    }
    try {
        const customerRecord = await customerRepo.findOne({
            where: { id: customerIdBigInt, storeId: convo.storeId, isDeleted: false },
        });
        const customerOrders = await orderRepo.find({
            where: { isDeleted: false, storeId: convo.storeId, customerId: customerIdBigInt },
            order: { createdAt: 'desc' },
            relations: { items: true },
            take: 5,
        });
        const currentCtx = await conversationRepo.findOne({
            where: { id: convo.id },
            select: ['context'],
        });
        const nextCtx = { ...(currentCtx?.context || {}), customer: customerRecord, customerOrders };
        await conversationRepo.update({ id: convo.id }, { context: nextCtx });
    }
    catch { }
    const underwayOrders = await orderRepo.find({
        where: {
            isDeleted: false,
            storeId: convo.storeId,
            customerId: customerIdBigInt,
            status: (0, typeorm_1.In)(shared_1.UNDERWAY_STATUSES),
        },
        relations: { items: true },
        order: { createdAt: 'desc' },
        take: 10,
    });
    if (!underwayOrders || underwayOrders.length === 0) {
        return { underway: false, message: 'No order currently underway.' };
    }
    if (items && items.length > 0) {
        let preparedIncoming = [];
        try {
            const resolved = await (0, shared_1.resolveItemsAgainstCatalog)(items, convo.storeId, db);
            preparedIncoming = resolved.map((it) => ({ productId: it.productId, quantity: it.quantity }));
        }
        catch { }
        const incomingKey = (list) => list
            .slice()
            .sort((a, b) => (a.productId > b.productId ? 1 : -1))
            .map((it) => `${it.productId.toString()}@${it.quantity}`)
            .join('|');
        const targetKey = preparedIncoming.length > 0 ? incomingKey(preparedIncoming) : undefined;
        const similar = underwayOrders.find((o) => {
            if (!o.items || o.items.length === 0 || !targetKey)
                return true;
            const k = incomingKey(o.items.map((it) => ({ productId: it.productId, quantity: Number(it.quantity) })));
            return k === targetKey;
        });
        if (similar) {
            return {
                underway: true,
                orderId: similar.id,
                orderNumber: similar.orderNumber,
                status: similar.status,
                total: similar.total,
                message: `An order is already underway for ${customerEmail || customerName}: ${similar.orderNumber}. Status: ${similar.status}.`,
            };
        }
    }
    const latest = underwayOrders[0];
    return {
        underway: true,
        orderId: latest.id,
        orderNumber: latest.orderNumber,
        status: latest.status,
        total: latest.total,
        message: `An order is already underway for ${customerEmail || customerName}: ${latest.orderNumber}. Status: ${latest.status}.`,
    };
}
exports.checkOrderUnderway = (0, dist_1.createTool)(checkOrderUnderwayTool, {
    name: 'checkOrderUnderway',
    description: 'Check if there is already an order underway for a customer. Provide either customerEmail or customerName. Optionally pass items to check for a similar order.',
    parameterTypes: {
        customerEmail: { type: 'string', optional: true },
        customerName: { type: 'string', optional: true },
        items: {
            type: 'array',
            optional: true,
            items: {
                type: 'object',
                properties: {
                    productId: { type: 'string', optional: true },
                    productName: { type: 'string', optional: true },
                    quantity: { type: 'number' },
                    taxAmount: { type: 'number', optional: true },
                },
                required: ['quantity'],
            },
        },
    },
    requiredParams: [],
});
//# sourceMappingURL=agent-tool-checkOrderUnderway.js.map
{"c": ["pages/live/[uuid]", "pages/conversations", "webpack"], "r": ["pages/orders", "pages/products", "pages/customers"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Corders%5Cindex.tsx&page=%2Forders!", "./src/components/EntityTable.tsx", "./src/components/OrderPrint.tsx", "./src/pages/orders/index.tsx", "./src/utils/print.ts", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cproducts%5Cindex.tsx&page=%2Fproducts!", "./src/components/ImageUpload.tsx", "./src/components/ProductModals.tsx", "./src/pages/products/index.tsx", "./src/utils/hooks.ts", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Ccustomers%5Cindex.tsx&page=%2Fcustomers!", "./src/components/CustomerModals.tsx", "./src/pages/customers/index.tsx"]}